# Reading Books List - Full Stack Application

A full-stack application for managing your reading list with a Node.js REST API backend and a Next.js React frontend.

## Project Structure

```
├── service/          # Node.js REST API backend
├── frontend/         # Next.js React frontend
└── README.md         # This file
```

## Features

### Backend API (Node.js + Express)
- **GET /reading-list/books** - Get all books
- **POST /reading-list/books** - Add a new book
- **GET /reading-list/books/:uuid** - Get a specific book
- **PUT /reading-list/books/:uuid** - Update book status
- **DELETE /reading-list/books/:uuid** - Delete a book
- **GET /healthz** - Health check endpoint

### Frontend (Next.js + React + TypeScript)
- **Book List View** - Display all books with status filtering
- **Add Book Form** - Add new books with title, author, and status
- **Status Management** - Update book status (to_read, reading, read)
- **Delete Books** - Remove books from the list
- **Responsive Design** - Works on desktop and mobile
- **Loading States** - Proper loading indicators
- **Error Handling** - User-friendly error messages

## Book Status Options
- `to_read` - Books you plan to read
- `reading` - Books you're currently reading  
- `read` - Books you've completed

## Getting Started

### Prerequisites
- Node.js (v14 or higher)
- npm

### Running the Backend
```bash
cd service
npm install
npm start
```
The API will be available at `http://localhost:8080`

### Running the Frontend
```bash
cd frontend
npm install
npm run dev
```
The frontend will be available at `http://localhost:3000`

## API Usage Examples

### Add a book
```bash
curl -X POST http://localhost:8080/reading-list/books \
  -H "Content-Type: application/json" \
  -d '{"title": "The Great Gatsby", "author": "F. Scott Fitzgerald", "status": "to_read"}'
```

### Get all books
```bash
curl http://localhost:8080/reading-list/books
```

### Update book status
```bash
curl -X PUT http://localhost:8080/reading-list/books/{uuid} \
  -H "Content-Type: application/json" \
  -d '{"status": "read"}'
```

### Delete a book
```bash
curl -X DELETE http://localhost:8080/reading-list/books/{uuid}
```

## Technology Stack

### Backend
- Node.js
- Express.js
- node-cache (in-memory storage)
- UUID for unique identifiers

### Frontend
- Next.js 15
- React 19
- TypeScript
- Tailwind CSS
- Modern React patterns (hooks, functional components)

## Development Notes

- The backend uses in-memory cache, so data is lost when the server restarts
- The frontend includes proper error handling and loading states
- CORS is not configured, so both services should run on localhost
- The frontend automatically connects to the backend API at `http://localhost:8080`

## Future Enhancements

- Add persistent database storage
- Implement user authentication
- Add book search and filtering
- Include book covers and descriptions
- Add reading progress tracking
- Implement data export/import
